* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #f3f1f5;
    color: #cda565;
    margin: 0;
    padding: 0;
    text-align: center;
    line-height: 1.6;
}

.thank-you-page {
    min-height: calc(100vh - 120px);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 130px 20px 30px 20px;
}

.container {
    padding: 30px 25px;
    max-width: 600px;
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(5px);
    text-align: center;
}

.thank-you h1 {
    font-size: 2.2em;
    margin-bottom: 25px;
    color: #cda565;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.thank-you p {
    font-size: 1em;
    margin-bottom: 20px;
    color: #222a31;
    text-align: center;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.back-button {
    display: inline-block;
    margin-top: 25px;
    font-size: 1em;
    color: #cda565;
    text-decoration: none;
    padding: 12px 30px;
    border: 2px solid #cda565;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(205, 165, 101, 0.15);
}

.back-button:hover {
    background-color: #cda565;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(205, 165, 101, 0.3);
}

/* Success Icon Styles */
.success-icon-container {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.success-icon-container::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(205, 165, 101, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.success-icon {
    width: 80px;
    height: 80px;
    animation: successAnimation 2s ease-in-out;
    filter: drop-shadow(0 2px 6px rgba(205, 165, 101, 0.2));
}

.success-circle {
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    animation: drawCircle 1.5s ease-in-out forwards;
    stroke-width: 3;
}

.success-checkmark {
    stroke-dasharray: 60;
    stroke-dashoffset: 60;
    animation: drawCheckmark 0.8s ease-in-out 1.2s forwards;
    stroke-width: 4;
}

/* Animations */
@keyframes successAnimation {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes drawCircle {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes drawCheckmark {
    to {
        stroke-dashoffset: 0;
    }
}

/* Hover effect for success icon */
.success-icon:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.phone-number {
    color: #cda565;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    padding: 2px 8px;
    border-radius: 5px;
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.1), rgba(205, 165, 101, 0.05));
}

.phone-number:hover {
    color: #fff;
    background: linear-gradient(135deg, #cda565, #b8935a);
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(205, 165, 101, 0.3);
}

.phone-number:active {
    transform: scale(0.98);
}

.timer {
    font-size: 1em;
    color: rgba(34, 42, 49, 0.6);
    margin-top: 25px;
    font-weight: 400;
}

/* მობილური დისპლეებისთვის */
@media (max-width: 768px) {
    .thank-you-page {
        padding: 120px 15px 25px 15px;
        min-height: calc(100vh - 120px);
    }

    .container {
        padding: 25px 20px;
        border-radius: 12px;
    }

    .thank-you h1 {
        font-size: 1.8em;
        margin-bottom: 20px;
    }

    .thank-you p {
        font-size: 0.95em;
        margin-bottom: 18px;
    }

    .back-button {
        padding: 10px 25px;
        font-size: 0.95em;
        margin-top: 20px;
    }

    .success-icon {
        width: 70px;
        height: 70px;
    }

    .success-icon-container::before {
        width: 90px;
        height: 90px;
    }

    .timer {
        font-size: 0.95em;
        margin-top: 20px;
    }
}

@media (max-width: 480px) {
    .thank-you-page {
        padding: 110px 10px 20px 10px;
    }

    .container {
        padding: 20px 15px;
        border-radius: 10px;
    }

    .thank-you h1 {
        font-size: 1.6em;
        margin-bottom: 18px;
    }

    .thank-you p {
        font-size: 0.9em;
        margin-bottom: 16px;
    }

    .back-button {
        padding: 10px 20px;
        font-size: 0.9em;
        margin-top: 18px;
    }

    .success-icon {
        width: 60px;
        height: 60px;
    }

    .success-icon-container::before {
        width: 80px;
        height: 80px;
    }

    .timer {
        font-size: 0.9em;
        margin-top: 18px;
    }
}
