* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f3f1f5;
    color: #cda565;
    margin: 0;
    padding: 0;
    text-align: center;
}

.thank-you-page {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 120px; /* Space for fixed header */
}

.container {
    padding: 20px;
    max-width: 600px;
}

.thank-you h1 {
    font-size: 2em;
    margin-bottom: 20px;
    color: #cda565; 
}

.thank-you p {
    font-size: 1em;
    margin-bottom: 20px;
    color: #222a31;
    text-align: justify;
}

.back-button {
    margin-top: 10px;
    font-size: 1em;
    color: #cda565; 
    text-decoration: none;
    padding: 12px 25px;
    border: 2px solid #cda565;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.back-button:hover {
    background-color: #cda565;
    color: #222a31;
}

/* Success Icon Styles */
.success-icon-container {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.success-icon {
    width: 120px;
    height: 120px;
    animation: successAnimation 2s ease-in-out;
}

.success-circle {
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    animation: drawCircle 1.5s ease-in-out forwards;
}

.success-checkmark {
    stroke-dasharray: 60;
    stroke-dashoffset: 60;
    animation: drawCheckmark 0.8s ease-in-out 1.2s forwards;
}

/* Animations */
@keyframes successAnimation {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes drawCircle {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes drawCheckmark {
    to {
        stroke-dashoffset: 0;
    }
}

/* Hover effect for success icon */
.success-icon:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.phone-number {
    color: #cda565;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s, transform 0.3s;
}

.phone-number:hover {
    color: #222a31;
    transform: scale(1.1); 
}

.phone-number:active {
    color: #f39c12; 
    transform: scale(1); 
}

.timer {
    font-size: 1em;
    color: #0000007f;
    margin-top: 20px;
}

/* მობილური დისპლეებისთვის */
@media (max-width: 768px) {
    body {
        font-size: 14px;
        padding: 10px;
    }

    .thank-you-page {
        padding-top: 100px;
        min-height: 90vh;
    }

    .container {
        padding: 15px;
    }

    .thank-you h1 {
        font-size: 1.5em;
    }

    .thank-you p {
        font-size: 0.9em;
    }

    .back-button {
        padding: 10px 20px;
        font-size: 0.9em;
    }

    .success-icon {
        width: 100px;
        height: 100px;
    }

    .timer {
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 12px;
    }

    .thank-you-page {
        padding-top: 90px;
    }

    .thank-you h1 {
        font-size: 1.2em;
    }

    .thank-you p {
        font-size: 0.8em;
    }

    .back-button {
        padding: 8px 15px;
        font-size: 0.8em;
    }

    .success-icon {
        width: 80px;
        height: 80px;
    }
}
