* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #f3f1f5;
    color: #cda565;
    margin: 0;
    padding: 0;
    text-align: center;
    line-height: 1.6;
}

.thank-you-page {
    min-height: calc(100vh - 120px);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 140px 20px 40px 20px; /* Top padding for header + extra space */
}

.container {
    padding: 40px 30px;
    max-width: 700px;
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.thank-you h1 {
    font-size: 2.5em;
    margin-bottom: 30px;
    color: #cda565;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thank-you p {
    font-size: 1.1em;
    margin-bottom: 25px;
    color: #222a31;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
}

.back-button {
    display: inline-block;
    margin-top: 30px;
    font-size: 1.1em;
    color: #cda565;
    text-decoration: none;
    padding: 15px 35px;
    border: 2px solid #cda565;
    border-radius: 50px;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(205, 165, 101, 0.2);
}

.back-button:hover {
    background-color: #cda565;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(205, 165, 101, 0.4);
}

/* Success Icon Styles */
.success-icon-container {
    margin-bottom: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.success-icon-container::before {
    content: '';
    position: absolute;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(205, 165, 101, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.success-icon {
    width: 150px;
    height: 150px;
    animation: successAnimation 2s ease-in-out;
    filter: drop-shadow(0 4px 8px rgba(205, 165, 101, 0.3));
}

.success-circle {
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    animation: drawCircle 1.5s ease-in-out forwards;
    stroke-width: 5;
}

.success-checkmark {
    stroke-dasharray: 60;
    stroke-dashoffset: 60;
    animation: drawCheckmark 0.8s ease-in-out 1.2s forwards;
    stroke-width: 7;
}

/* Animations */
@keyframes successAnimation {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes drawCircle {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes drawCheckmark {
    to {
        stroke-dashoffset: 0;
    }
}

/* Hover effect for success icon */
.success-icon:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.phone-number {
    color: #cda565;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    padding: 2px 8px;
    border-radius: 5px;
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.1), rgba(205, 165, 101, 0.05));
}

.phone-number:hover {
    color: #fff;
    background: linear-gradient(135deg, #cda565, #b8935a);
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(205, 165, 101, 0.3);
}

.phone-number:active {
    transform: scale(0.98);
}

.timer {
    font-size: 1.1em;
    color: rgba(34, 42, 49, 0.6);
    margin-top: 30px;
    font-weight: 500;
}

/* მობილური დისპლეებისთვის */
@media (max-width: 768px) {
    .thank-you-page {
        padding: 120px 15px 30px 15px;
        min-height: calc(100vh - 120px);
    }

    .container {
        padding: 30px 20px;
        border-radius: 15px;
    }

    .thank-you h1 {
        font-size: 2em;
        margin-bottom: 25px;
    }

    .thank-you p {
        font-size: 1em;
        margin-bottom: 20px;
    }

    .back-button {
        padding: 12px 25px;
        font-size: 1em;
        margin-top: 25px;
    }

    .success-icon {
        width: 120px;
        height: 120px;
    }

    .success-icon-container::before {
        width: 150px;
        height: 150px;
    }

    .timer {
        font-size: 1em;
        margin-top: 25px;
    }
}

@media (max-width: 480px) {
    .thank-you-page {
        padding: 110px 10px 20px 10px;
    }

    .container {
        padding: 25px 15px;
        border-radius: 12px;
    }

    .thank-you h1 {
        font-size: 1.8em;
        margin-bottom: 20px;
    }

    .thank-you p {
        font-size: 0.95em;
        margin-bottom: 18px;
    }

    .back-button {
        padding: 10px 20px;
        font-size: 0.95em;
        margin-top: 20px;
    }

    .success-icon {
        width: 100px;
        height: 100px;
    }

    .success-icon-container::before {
        width: 120px;
        height: 120px;
    }

    .timer {
        font-size: 0.95em;
        margin-top: 20px;
    }
}
